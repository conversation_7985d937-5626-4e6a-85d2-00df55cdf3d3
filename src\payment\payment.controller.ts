/**
 * Payment Controller
 *
 * Handles all payment-related HTTP endpoints for the Career Ireland platform.
 * This controller provides REST API endpoints for processing payments across
 * multiple service types including mentor services, packages, immigration
 * services, and training programs.
 *
 * Features:
 * - Authenticated user payments (requires JWT token)
 * - Guest user payments (no authentication required)
 * - Stripe webhook handling for payment confirmations
 * - Support for multiple service types
 *
 * Security:
 * - JWT authentication for user endpoints
 * - Input validation using DTOs
 * - Stripe webhook signature verification
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */

import {
  Body,
  Controller,
  Post,
  RawBodyRequest,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaymentService } from './payment.service';
import { JwtGuard } from 'src/guards/jwt.guard';
import {
  UserImmigrationServiceDto,
  UserMentorServiceDto,
  UserPackageServiceDto,
  UserTrainingServiceDto,
} from './dto/payment.dto';
import { GetUser } from 'src/decorator/user.decorator';
import { FastifyRequest } from 'fastify';

@ApiTags('payment')
@Controller('payment')
export class PaymentController {
  /**
   * Payment Controller Constructor
   *
   * Initializes the payment controller with the payment service dependency.
   *
   * @param payment - Payment service instance for handling payment operations
   */
  constructor(private payment: PaymentService) {}

  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Post('/mentor-service')
  @ApiOperation({
    summary: '(User only)',
    description:
      'This API is restricted to users and requires a Bearer token for authentication.',
  })
  async create(
    @GetUser() user: IJWTPayload,
    @Body() dto: UserMentorServiceDto,
  ) {
    return await this.payment.mentor_service(user, dto);
  }
  @Post('/guest-service')
  @ApiOperation({
    summary: '(Guest User only)',
  })
  async guestService(@Body() dto: UserMentorServiceDto) {
    return await this.payment.guest_service(dto);
  }

  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Post('/package')
  @ApiOperation({
    summary: '(User only)',
    description:
      'This API is restricted to users and requires a Bearer token for authentication.',
  })
  async user_package(
    @GetUser() user: IJWTPayload,
    @Body() dto: UserPackageServiceDto,
  ) {
    return await this.payment.user_package(user, dto);
  }
  @Post('/guest-package')
  @ApiOperation({
    summary: '(Guest only)',
  })
  async guestPackage(@Body() dto: UserPackageServiceDto) {
    return await this.payment.guest_package(dto);
  }

  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Post('/immigration-service')
  @ApiOperation({
    summary: '(User only)',
    description:
      'This API is restricted to users and requires a Bearer token for authentication.',
  })
  async user_immigration(
    @GetUser() user: IJWTPayload,
    @Body() dto: UserImmigrationServiceDto,
  ) {
    return await this.payment.user_immigration(user, dto);
  }
  @Post('/guest-immigration')
  @ApiOperation({
    summary: '(Guest only)',
  })
  async guestImmigration(@Body() dto: UserImmigrationServiceDto) {
    return await this.payment.guest_immigration(dto);
  }

  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Post('/training')
  @ApiOperation({
    summary: '(User only)',
    description:
      'This API is restricted to users and requires a Bearer token for authentication.',
  })
  async user_training(
    @GetUser() user: IJWTPayload,
    @Body() dto: UserTrainingServiceDto,
  ) {
    return await this.payment.user_training(user, dto);
  }
  @Post('/guest-training')
  @ApiOperation({
    summary: '(Guest only)',
  })
  async guestTraining(@Body() dto: UserTrainingServiceDto) {
    return await this.payment.guest_training(dto);
  }

  @Post('/web-hook')
  async webhook(@Req() req: RawBodyRequest<FastifyRequest>) {
    return await this.payment.webhook(req);
  }
}
