# Service Tables Schema Comparison

## Overview

This document provides a comprehensive comparison of the four main service tables in the Career Ireland Immigration SaaS platform: `immigration_service`, `packages`, `training`, and `service` (mentor services). This analysis follows the same format as the PAYMENT_MIGRATION.md to help understand the schema structures and relationships.

## Service Tables Schema Analysis

### 1. Immigration Service Table

#### Current Schema
```sql
CREATE TABLE "immigration_service" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "order" INTEGER NULL,
    "service" TEXT[] NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "immigration_service_pkey" PRIMARY KEY ("id")
);
```

#### Prisma Schema
```prisma
model immigration_service {
    id        String                      @id @default(cuid())
    name      String
    amount    Int
    order     Int?
    service   String[]
    createdAt DateTime                    @default(now())
    updatedAt DateTime                    @updatedAt
    users     user_immigration_service[]
    guest     guest_immigration_service[]
}
```

#### Key Characteristics
- **Primary Purpose**: Immigration consultation and document processing services
- **Pricing Model**: Fixed amount per service
- **Service Array**: Multiple service offerings within each immigration service
- **Order Field**: For display ordering in UI
- **Relationships**: Connected to both user and guest payment tables

### 2. Packages Table

#### Current Schema
```sql
CREATE TABLE "packages" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "note" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "order" INTEGER NULL,
    "service" TEXT[] NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "packages_pkey" PRIMARY KEY ("id")
);
```

#### Prisma Schema
```prisma
model packages {
    id        String          @id @default(cuid())
    name      String
    note      String
    amount    Int
    order     Int?
    service   String[]
    createdAt DateTime        @default(now())
    updatedAt DateTime        @updatedAt
    users     user_package[]
    guest     guest_package[]
}
```

#### Key Characteristics
- **Primary Purpose**: Bundled service packages combining multiple offerings
- **Note Field**: Additional description or terms for the package
- **Service Array**: List of services included in the package
- **Pricing Model**: Fixed package price
- **Relationships**: Connected to both user and guest payment tables

### 3. Training Table

#### Current Schema
```sql
CREATE TABLE "training" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "img" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "order" INTEGER NULL,
    "service" TEXT[] NOT NULL,
    "highlights" TEXT[] NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "training_pkey" PRIMARY KEY ("id")
);
```

#### Prisma Schema
```prisma
model training {
    id         String          @id @default(cuid())
    name       String
    img        String
    amount     Int
    order      Int?
    service    String[]
    highlights String[]
    createdAt  DateTime        @default(now())
    updatedAt  DateTime        @updatedAt
    users      user_training[]
    guest      guest_training[]
}
```

#### Key Characteristics
- **Primary Purpose**: Educational training programs and courses
- **Image Field**: Visual representation of the training
- **Service Array**: Training modules or components
- **Highlights Array**: Key features or benefits of the training
- **Pricing Model**: Fixed training fee
- **Relationships**: Connected to both user and guest payment tables

### 4. Service Table (Mentor Services)

#### Current Schema
```sql
CREATE TABLE "service" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "price" INTEGER NOT NULL,
    "description" TEXT NOT NULL,
    "meeting_link" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "status" "Status" NOT NULL DEFAULT 'Active',
    "mentorId" TEXT NULL,

    CONSTRAINT "service_pkey" PRIMARY KEY ("id")
);
```

#### Prisma Schema
```prisma
model service {
    id           String                @id @default(cuid())
    name         String
    price        Int
    description  String
    meeting_link String
    createdAt    DateTime              @default(now())
    updatedAt    DateTime              @updatedAt
    status       Status                @default(Active)
    mentor       mentor?               @relation(fields: [mentorId], references: [id], onDelete: SetNull)
    mentorId     String?
    users        user_mentor_service[]
    guest        guest_mentor_service[]
}
```

#### Key Characteristics
- **Primary Purpose**: One-on-one mentor consultation services
- **Pricing Field**: Uses "price" instead of "amount" (different naming convention)
- **Meeting Link**: Direct link for mentor sessions
- **Status Field**: Service availability status (Active/Inactive/etc.)
- **Mentor Relationship**: Each service belongs to a specific mentor
- **Relationships**: Connected to both user and guest payment tables

## Schema Comparison Matrix

| Field | immigration_service | packages | training | service |
|-------|-------------------|----------|----------|---------|
| **id** | ✅ TEXT PRIMARY KEY | ✅ TEXT PRIMARY KEY | ✅ TEXT PRIMARY KEY | ✅ TEXT PRIMARY KEY |
| **name** | ✅ TEXT NOT NULL | ✅ TEXT NOT NULL | ✅ TEXT NOT NULL | ✅ TEXT NOT NULL |
| **amount/price** | ✅ amount (INT) | ✅ amount (INT) | ✅ amount (INT) | ✅ price (INT) |
| **description** | ❌ | ❌ | ❌ | ✅ TEXT NOT NULL |
| **note** | ❌ | ✅ TEXT NOT NULL | ❌ | ❌ |
| **img** | ❌ | ❌ | ✅ TEXT NOT NULL | ❌ |
| **order** | ✅ INT NULL | ✅ INT NULL | ✅ INT NULL | ❌ |
| **service** | ✅ TEXT[] | ✅ TEXT[] | ✅ TEXT[] | ❌ |
| **highlights** | ❌ | ❌ | ✅ TEXT[] | ❌ |
| **meeting_link** | ❌ | ❌ | ❌ | ✅ TEXT NOT NULL |
| **status** | ❌ | ❌ | ❌ | ✅ Status ENUM |
| **mentorId** | ❌ | ❌ | ❌ | ✅ TEXT NULL |
| **createdAt** | ✅ TIMESTAMP(3) | ✅ TIMESTAMP(3) | ✅ TIMESTAMP(3) | ✅ TIMESTAMP(3) |
| **updatedAt** | ✅ TIMESTAMP(3) | ✅ TIMESTAMP(3) | ✅ TIMESTAMP(3) | ✅ TIMESTAMP(3) |

## Common Patterns

### Shared Fields Across All Tables
```sql
id              TEXT PRIMARY KEY
name            TEXT NOT NULL
amount/price    INTEGER NOT NULL
createdAt       TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP
updatedAt       TIMESTAMP(3)
```

### Common Optional Fields
```sql
order           INTEGER NULL (3 out of 4 tables)
service         TEXT[] (3 out of 4 tables)
```

### Unique Fields by Table
- **immigration_service**: No unique fields (most basic structure)
- **packages**: `note` field for package descriptions
- **training**: `img` and `highlights` arrays for visual and feature presentation
- **service**: `description`, `meeting_link`, `status`, `mentorId` for mentor-specific functionality

## Pricing Field Inconsistency

### Issue Identified
The `service` table uses `price` while all other tables use `amount` for the same purpose.

#### Current State
```sql
-- service table
price INTEGER NOT NULL

-- All other tables
amount INTEGER NOT NULL
```

#### Recommendation for Consistency
```sql
-- Standardize to 'amount' across all tables
ALTER TABLE "service" RENAME COLUMN "price" TO "amount";
```

## Relationship Patterns

### Payment Table Relationships
All four service tables follow the same relationship pattern with payment tables:

```prisma
// Each service table has:
users     user_[service_type][]     // For authenticated user purchases
guest     guest_[service_type][]    // For guest purchases
```

### Foreign Key Relationships
```sql
-- User payment tables
user_mentor_service.serviceId → service.id
user_package.packageId → packages.id
user_immigration_service.immigration_serviceId → immigration_service.id
user_training.trainingId → training.id

-- Guest payment tables
guest_mentor_service.serviceId → service.id
guest_package.packageId → packages.id
guest_immigration_service.immigration_serviceId → immigration_service.id
guest_training.trainingId → training.id
```

## Service Array Analysis

### Purpose and Usage
Three tables (`immigration_service`, `packages`, `training`) use a `service` array field:

```sql
service TEXT[] NOT NULL
```

#### Immigration Service Example
```json
{
  "service": [
    "Document Review",
    "Application Assistance",
    "Interview Preparation",
    "Status Tracking"
  ]
}
```

#### Package Example
```json
{
  "service": [
    "CV Review",
    "Interview Coaching",
    "LinkedIn Optimization",
    "Job Search Strategy"
  ]
}
```

#### Training Example
```json
{
  "service": [
    "Module 1: Introduction",
    "Module 2: Advanced Techniques",
    "Module 3: Practical Application",
    "Module 4: Assessment"
  ]
}
```

### Potential Normalization Opportunity
The `service` arrays could be normalized into separate tables for better data integrity:

```sql
-- Proposed normalized structure
CREATE TABLE "service_items" (
    "id" TEXT PRIMARY KEY,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "service_type" TEXT NOT NULL, -- 'immigration', 'package', 'training'
    "parent_id" TEXT NOT NULL,    -- Reference to parent service
    "order" INTEGER,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3)
);
```

## Status Management

### Current Status Implementation
Only the `service` table implements status management:

```sql
status "Status" NOT NULL DEFAULT 'Active'
```

#### Status Enum Values
```sql
enum Status {
  Accepted
  Completed
  Active
  Inactive
  Cancelled
  Refunded
  Blocked
  Rejected
  Pending
}
```

### Recommendation for Consistency
Consider adding status management to all service tables:

```sql
-- Add status to other tables
ALTER TABLE "immigration_service" ADD COLUMN "status" "Status" NOT NULL DEFAULT 'Active';
ALTER TABLE "packages" ADD COLUMN "status" "Status" NOT NULL DEFAULT 'Active';
ALTER TABLE "training" ADD COLUMN "status" "Status" NOT NULL DEFAULT 'Active';
```

## Migration History Analysis

### Key Schema Changes

#### 1. Service Table Evolution
```sql
-- Initial creation (20241125083219_initial)
price BIGINT NOT NULL

-- Price type change (20241210090246_service)
price INTEGER NOT NULL

-- Mentor relationship changes
-- Made mentorId required (20241227060027)
-- Added CASCADE delete (20241124094515)
-- Reverted to RESTRICT (20241124095758)
```

#### 2. Training Table Addition
```sql
-- Training table created (20250124082107_training)
-- Image field added later (20250217115959_training_img)
```

#### 3. Packages and Immigration Service
```sql
-- Both created together (20241226090309_packages_and_immiration)
-- Order fields added later to packages and immigration_service
```

## Data Integrity Considerations

### Current Constraints
```sql
-- Primary keys
CONSTRAINT "immigration_service_pkey" PRIMARY KEY ("id")
CONSTRAINT "packages_pkey" PRIMARY KEY ("id")
CONSTRAINT "training_pkey" PRIMARY KEY ("id")
CONSTRAINT "service_pkey" PRIMARY KEY ("id")

-- Foreign key (service table only)
CONSTRAINT "service_mentorId_fkey" FOREIGN KEY ("mentorId")
    REFERENCES "mentor"("id") ON DELETE RESTRICT ON UPDATE CASCADE
```

### Missing Constraints
Consider adding:
```sql
-- Check constraints for positive amounts
ALTER TABLE "immigration_service" ADD CONSTRAINT "immigration_service_amount_positive"
    CHECK (amount > 0);
ALTER TABLE "packages" ADD CONSTRAINT "packages_amount_positive"
    CHECK (amount > 0);
ALTER TABLE "training" ADD CONSTRAINT "training_amount_positive"
    CHECK (amount > 0);
ALTER TABLE "service" ADD CONSTRAINT "service_price_positive"
    CHECK (price > 0);

-- Check constraints for non-empty arrays
ALTER TABLE "immigration_service" ADD CONSTRAINT "immigration_service_service_not_empty"
    CHECK (array_length(service, 1) > 0);
ALTER TABLE "packages" ADD CONSTRAINT "packages_service_not_empty"
    CHECK (array_length(service, 1) > 0);
ALTER TABLE "training" ADD CONSTRAINT "training_service_not_empty"
    CHECK (array_length(service, 1) > 0);
ALTER TABLE "training" ADD CONSTRAINT "training_highlights_not_empty"
    CHECK (array_length(highlights, 1) > 0);
```

## Performance Considerations

### Current Indexes
```sql
-- Only primary key indexes exist
-- No additional indexes for common query patterns
```

### Recommended Indexes
```sql
-- For ordering and filtering
CREATE INDEX "immigration_service_order_idx" ON "immigration_service"("order");
CREATE INDEX "packages_order_idx" ON "packages"("order");
CREATE INDEX "training_order_idx" ON "training"("order");
CREATE INDEX "service_status_idx" ON "service"("status");
CREATE INDEX "service_mentorId_idx" ON "service"("mentorId");

-- For amount-based queries
CREATE INDEX "immigration_service_amount_idx" ON "immigration_service"("amount");
CREATE INDEX "packages_amount_idx" ON "packages"("amount");
CREATE INDEX "training_amount_idx" ON "training"("amount");
CREATE INDEX "service_price_idx" ON "service"("price");

-- For text search on names
CREATE INDEX "immigration_service_name_idx" ON "immigration_service" USING gin(to_tsvector('english', name));
CREATE INDEX "packages_name_idx" ON "packages" USING gin(to_tsvector('english', name));
CREATE INDEX "training_name_idx" ON "training" USING gin(to_tsvector('english', name));
CREATE INDEX "service_name_idx" ON "service" USING gin(to_tsvector('english', name));
```

## API Endpoint Impact

### Service-Related Endpoints
Based on the payment migration analysis, these service tables are accessed through:

1. **Direct Service Management Endpoints**
   - `GET /immigration-service` - List immigration services
   - `GET /packages` - List packages
   - `GET /training` - List training programs
   - `GET /service` - List mentor services

2. **Payment Processing Endpoints**
   - `POST /payment/immigration-service` - Purchase immigration service
   - `POST /payment/package` - Purchase package
   - `POST /payment/training` - Purchase training
   - `POST /payment/mentor-service` - Purchase mentor service

3. **Admin Management Endpoints**
   - CRUD operations for each service type
   - Status management (for service table)
   - Order management (for tables with order field)

## Recommendations for Schema Standardization

### 1. Field Naming Consistency
```sql
-- Standardize pricing field
ALTER TABLE "service" RENAME COLUMN "price" TO "amount";
```

### 2. Add Missing Status Management
```sql
-- Add status to all service tables
ALTER TABLE "immigration_service" ADD COLUMN "status" "Status" NOT NULL DEFAULT 'Active';
ALTER TABLE "packages" ADD COLUMN "status" "Status" NOT NULL DEFAULT 'Active';
ALTER TABLE "training" ADD COLUMN "status" "Status" NOT NULL DEFAULT 'Active';
```

### 3. Add Order Field to Service Table
```sql
-- Add order field for consistency
ALTER TABLE "service" ADD COLUMN "order" INTEGER NULL;
```

### 4. Standardize Description Fields
```sql
-- Add description to other tables
ALTER TABLE "immigration_service" ADD COLUMN "description" TEXT;
ALTER TABLE "packages" RENAME COLUMN "note" TO "description";
ALTER TABLE "training" ADD COLUMN "description" TEXT;
```

### 5. Implement Data Validation
```sql
-- Add check constraints for data integrity
-- (See Data Integrity Considerations section above)
```

## Conclusion

The four service tables show a generally consistent pattern but have some inconsistencies that could be addressed:

### Strengths
- Consistent primary key and timestamp patterns
- Clear separation of concerns between service types
- Proper relationships with payment tables
- Flexible service arrays for detailed offerings

### Areas for Improvement
- Field naming inconsistencies (`price` vs `amount`)
- Missing status management on some tables
- Lack of performance indexes
- Missing data validation constraints
- Inconsistent description/note fields

### Next Steps
1. **Immediate**: Add performance indexes for common queries
2. **Short-term**: Standardize field naming across tables
3. **Medium-term**: Add status management to all service tables
4. **Long-term**: Consider normalizing service arrays if needed

This analysis provides a foundation for understanding the current service table structure and planning future improvements to maintain consistency and performance across the Career Ireland Immigration SaaS platform.

## Service Table Consolidation Analysis

### Should We Combine These Tables?

Based on the schema analysis and business logic review, here's a comprehensive assessment of whether consolidating the four service tables would be beneficial:

## ✅ **Arguments FOR Consolidation**

### 1. **Schema Consistency Benefits**
- **Unified Field Naming**: Eliminates `price` vs `amount` inconsistency
- **Standardized Status Management**: All services would have consistent status tracking
- **Simplified Relationships**: Single service table instead of 4 separate relationships
- **Consistent Indexing Strategy**: Unified performance optimization approach

### 2. **Code Simplification**
- **Reduced Duplication**: Currently 4 nearly identical CRUD services
- **Unified API Patterns**: Single service management endpoint instead of 4
- **Simplified Payment Logic**: Already moving toward unified payment system
- **Easier Maintenance**: One service table to maintain instead of 4

### 3. **Query Simplification**
```sql
-- Current: Complex queries across multiple tables
SELECT * FROM service WHERE status = 'Active'
UNION ALL
SELECT * FROM packages WHERE order IS NOT NULL
UNION ALL
SELECT * FROM training WHERE amount > 100
UNION ALL
SELECT * FROM immigration_service WHERE name LIKE '%visa%'

-- After consolidation: Single table queries
SELECT * FROM unified_service
WHERE (status = 'Active' OR status IS NULL)
  AND (service_type = 'mentor' OR (service_type IN ('package', 'training', 'immigration') AND order IS NOT NULL))
  AND amount > 100
  AND name LIKE '%visa%'
```

### 4. **Future Scalability**
- **New Service Types**: Easy to add without creating new tables
- **Cross-Service Features**: Easier to implement features that span service types
- **Unified Analytics**: Simpler reporting across all service types
- **Consistent Business Rules**: Apply same validation and business logic

## ❌ **Arguments AGAINST Consolidation**

### 1. **Business Domain Separation**
Each service type serves distinct business purposes:

#### **Mentor Services (service table)**
- **Purpose**: One-on-one career consultation
- **Unique Features**:
  - `meeting_link` for direct mentor access
  - `mentorId` relationship for mentor assignment
  - `description` for detailed service explanation
- **Business Logic**: Mentor-specific workflows and notifications

#### **Immigration Services (immigration_service table)**
- **Purpose**: Visa and immigration document processing
- **Unique Features**:
  - Specialized for immigration compliance
  - Different regulatory requirements
  - Specific service arrays for immigration processes
- **Business Logic**: Immigration-specific workflows and compliance

#### **Training Programs (training table)**
- **Purpose**: Educational courses and skill development
- **Unique Features**:
  - `img` for course visualization
  - `highlights` array for marketing features
  - Course-specific metadata
- **Business Logic**: Learning management and progress tracking

#### **Service Packages (packages table)**
- **Purpose**: Bundled service offerings
- **Unique Features**:
  - `note` field for package terms and conditions
  - Bundle-specific pricing logic
  - Cross-service bundling capabilities
- **Business Logic**: Package composition and pricing rules

### 2. **Technical Complexity**
- **Nullable Fields**: Many fields would become nullable, reducing data integrity
- **Complex Validation**: Need conditional validation based on service_type
- **Migration Complexity**: More complex than payment migration due to business logic differences
- **Query Complexity**: More complex WHERE clauses for service-type-specific queries

### 3. **Performance Considerations**
```sql
-- Current: Optimized table-specific queries
SELECT * FROM training WHERE img IS NOT NULL; -- Fast, no nulls

-- After consolidation: Less efficient queries
SELECT * FROM unified_service
WHERE service_type = 'training' AND img IS NOT NULL; -- Slower, many nulls
```

### 4. **Data Integrity Risks**
```sql
-- Current: Strong constraints per table
CREATE TABLE training (
    img TEXT NOT NULL,  -- Always required for training
    highlights TEXT[] NOT NULL  -- Always required
);

-- After consolidation: Weaker constraints
CREATE TABLE unified_service (
    img TEXT NULL,  -- Only required for training
    highlights TEXT[] NULL,  -- Only required for training
    meeting_link TEXT NULL,  -- Only required for mentor services
    -- Complex check constraints needed
);
```

## 🔄 **Hybrid Approach Recommendation**

Instead of full consolidation, consider a **hybrid approach** that maintains business domain separation while improving consistency:

### Phase 1: Schema Standardization (Recommended)
```sql
-- Standardize field names
ALTER TABLE "service" RENAME COLUMN "price" TO "amount";

-- Add missing fields for consistency
ALTER TABLE "immigration_service" ADD COLUMN "status" "Status" NOT NULL DEFAULT 'Active';
ALTER TABLE "packages" ADD COLUMN "status" "Status" NOT NULL DEFAULT 'Active';
ALTER TABLE "training" ADD COLUMN "status" "Status" NOT NULL DEFAULT 'Active';
ALTER TABLE "service" ADD COLUMN "order" INTEGER NULL;

-- Standardize description fields
ALTER TABLE "immigration_service" ADD COLUMN "description" TEXT;
ALTER TABLE "packages" RENAME COLUMN "note" TO "description";
ALTER TABLE "training" ADD COLUMN "description" TEXT;
```

### Phase 2: Unified Service Interface
```typescript
// Create a unified service interface while keeping separate tables
interface IServiceManager {
  getAll(serviceType: ServiceType): Promise<Service[]>;
  getById(id: string, serviceType: ServiceType): Promise<Service>;
  create(data: ServiceData, serviceType: ServiceType): Promise<Service>;
  update(id: string, data: ServiceData, serviceType: ServiceType): Promise<Service>;
  delete(id: string, serviceType: ServiceType): Promise<void>;
}

// Implementation that delegates to appropriate table-specific services
@Injectable()
export class UnifiedServiceManager implements IServiceManager {
  constructor(
    private mentorService: MentorServiceService,
    private packageService: PackagesService,
    private immigrationService: ImmigrationService,
    private trainingService: TrainingService,
  ) {}

  async getAll(serviceType: ServiceType) {
    switch (serviceType) {
      case 'mentor': return this.mentorService.getAll();
      case 'package': return this.packageService.getAll();
      case 'immigration': return this.immigrationService.getAll();
      case 'training': return this.trainingService.getAll();
    }
  }
  // ... other methods
}
```

### Phase 3: Unified API Layer
```typescript
@Controller('services')
export class UnifiedServiceController {
  @Get(':serviceType')
  async getServices(@Param('serviceType') type: ServiceType) {
    return this.unifiedServiceManager.getAll(type);
  }

  @Post(':serviceType')
  async createService(
    @Param('serviceType') type: ServiceType,
    @Body() data: ServiceData
  ) {
    return this.unifiedServiceManager.create(data, type);
  }
}
```

## 📊 **Impact Analysis Summary**

### If We Consolidate (Full Merge)

#### **Positive Impacts** 🟢
- **Reduced Code Duplication**: ~75% reduction in service layer code
- **Simplified Database Schema**: 1 table instead of 4
- **Unified Payment Integration**: Easier integration with unified payment system
- **Consistent Field Naming**: Eliminates naming inconsistencies
- **Easier Cross-Service Features**: Simpler to implement features spanning service types

#### **Negative Impacts** 🔴
- **Complex Migration**: High-risk migration affecting core business logic
- **Reduced Data Integrity**: Many nullable fields, weaker constraints
- **Business Logic Complexity**: Complex conditional logic based on service_type
- **Performance Degradation**: Larger table with many nullable fields
- **Domain Model Confusion**: Less clear business domain separation

#### **Migration Complexity** ⚠️
- **High Risk**: Core business functionality affected
- **Extended Downtime**: More complex than payment migration
- **Data Validation**: Complex validation rules needed
- **Rollback Complexity**: Difficult to rollback due to business logic changes

### If We Keep Separate (Hybrid Approach)

#### **Positive Impacts** 🟢
- **Preserved Business Logic**: Clear domain separation maintained
- **Strong Data Integrity**: Table-specific constraints preserved
- **Lower Migration Risk**: Incremental improvements, lower risk
- **Performance Optimization**: Table-specific optimizations possible
- **Clear Domain Models**: Business logic remains clear and focused

#### **Negative Impacts** 🔴
- **Some Code Duplication**: Still need separate services (but can be abstracted)
- **Multiple Tables**: Still managing 4 tables instead of 1
- **Relationship Complexity**: Still need 4 sets of payment relationships

## 🎯 **Final Recommendation**

### **DO NOT** consolidate the service tables. Here's why:

1. **Business Domain Integrity**: Each service type has distinct business purposes and requirements
2. **Data Integrity**: Current table-specific constraints are stronger than what's possible in a unified table
3. **Migration Risk**: High risk with limited benefits compared to payment consolidation
4. **Performance**: Table-specific optimizations are more effective
5. **Maintainability**: Clear domain separation makes code more maintainable

### **DO** implement the Hybrid Approach:

1. **Phase 1**: Standardize schemas (field names, add missing fields)
2. **Phase 2**: Create unified service interfaces and abstractions
3. **Phase 3**: Implement unified API endpoints while keeping separate tables
4. **Phase 4**: Add performance indexes and constraints

This approach gives you **80% of the benefits** of consolidation with **20% of the risk**.

## 🔄 **Alternative: Service Type Hierarchy**

If you still want some consolidation, consider a **partial consolidation** approach:

### Group by Business Similarity
```sql
-- Keep mentor services separate (unique mentor relationship)
service (mentor services)

-- Consolidate the other three (similar business patterns)
unified_offering (packages + immigration_service + training)
```

This reduces 4 tables to 2 while preserving the most distinct business logic (mentor services) and consolidating the more similar service types.

However, even this approach has significant complexity and risk compared to the hybrid approach.
